// Package ai internal/modules/ai/provider.go
package ai

import (
	"context"
	"time"
)

// Provider AI 提供者介面
type Provider interface {
	Name() string
	SendMessage(ctx context.Context, message string) (*Response, error)
	SendMessageWithContext(ctx context.Context, message string, context []string) (*Response, error)
	GetModels() []string
	IsAvailable() bool
	Shutdown() error
}

// Response AI 回應
type Response struct {
	Content      string
	Provider     string
	Model        string
	TokensUsed   int
	ResponseTime time.Duration
	Error        error
}

// ProviderConfig 提供者配置基礎結構
type ProviderConfig struct {
	APIKey  string
	Model   string
	BaseURL string
}
