// Package taskrunner internal/modules/taskrunner/manager.go
package taskrunner

import (
	"context"
	"fmt"
	"sync"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/data/binding"

	"assistant-go/internal/config"
	"assistant-go/internal/modules"
	"assistant-go/internal/utils"
)

// Manager 任務執行器管理器
type Manager struct {
	config      *config.TaskRunnerConfig
	tasks       []Task
	executions  []TaskExecution
	currentExec *TaskExecution
	execMutex   sync.Mutex
	logger      *utils.Logger

	// UI 元件
	ui *UI

	// 資料綁定
	selectedTask int
	searchFilter binding.String
	outputBuffer binding.String

	// 執行控制
	cancelFunc context.CancelFunc
}

// NewManager 建立新的任務執行器管理器
func NewManager(cfg *config.TaskRunnerConfig, logger *utils.Logger) *Manager {
	return &Manager{
		config:       cfg,
		tasks:        []Task{},
		executions:   []TaskExecution{},
		selectedTask: -1,
		searchFilter: binding.NewString(),
		outputBuffer: binding.NewString(),
		logger:       logger,
	}
}

// Initialize 實作 Module 介面
func (m *Manager) Initialize(ctx context.Context) error {
	m.logger.Info("Initializing TaskRunner module...")

	// 掃描並載入任務
	if err := m.scanTasks(); err != nil {
		return utils.WrapError(err, utils.ErrCodeModule, "failed to scan tasks")
	}

	// 建立 UI
	m.ui = NewUI(m)

	m.logger.Info("TaskRunner module initialized successfully")
	return nil
}

// Content 實作 Module 介面
func (m *Manager) Content() fyne.CanvasObject {
	if m.ui == nil {
		return container.NewVBox()
	}
	return m.ui.Content()
}

// Refresh 實作 Module 介面
func (m *Manager) Refresh() error {
	m.logger.Info("Refreshing TaskRunner module...")
	
	if err := m.scanTasks(); err != nil {
		return utils.WrapError(err, utils.ErrCodeModule, "failed to refresh tasks")
	}

	if m.ui != nil {
		m.ui.Refresh()
	}

	return nil
}

// Shutdown 實作 Module 介面
func (m *Manager) Shutdown() error {
	m.logger.Info("Shutting down TaskRunner module...")

	// 取消正在執行的任務
	if m.cancelFunc != nil {
		m.cancelFunc()
	}

	// 等待當前執行完成
	m.execMutex.Lock()
	defer m.execMutex.Unlock()

	m.logger.Info("TaskRunner module shut down successfully")
	return nil
}

// Info 實作 Module 介面
func (m *Manager) Info() modules.ModuleInfo {
	return modules.ModuleInfo{
		ID:          "taskrunner",
		Name:        "Task Runner",
		Description: "Execute Makefile and Taskfile tasks",
		Version:     "1.0.0",
		Author:      "Koopa",
	}
}

// scanTasks 掃描 Makefile 和 Taskfile
func (m *Manager) scanTasks() error {
	m.tasks = []Task{}

	// 1. 添加內建 Go 常用命令
	m.addBuiltinGoTasks()

	// 2. 掃描專案路徑中的任務檔案
	for _, projectPath := range m.config.ProjectPaths {
		// 掃描 Makefile
		if tasks, err := m.parseMakefile(projectPath); err == nil {
			m.tasks = append(m.tasks, tasks...)
		}

		// 掃描 Taskfile
		if tasks, err := m.parseTaskfile(projectPath); err == nil {
			m.tasks = append(m.tasks, tasks...)
		}
	}

	// 依照類別和名稱排序任務
	m.sortTasks()

	return nil
}

// GetTasks 取得所有任務
func (m *Manager) GetTasks() []Task {
	return m.tasks
}

// GetFilteredTasks 取得過濾後的任務
func (m *Manager) GetFilteredTasks() []Task {
	filter, _ := m.searchFilter.Get()
	if filter == "" {
		return m.tasks
	}

	var filtered []Task
	for _, task := range m.tasks {
		if m.matchFilter(task, filter) {
			filtered = append(filtered, task)
		}
	}
	return filtered
}

// ExecuteTask 執行任務
func (m *Manager) ExecuteTask(taskIndex int) error {
	if taskIndex < 0 || taskIndex >= len(m.tasks) {
		return fmt.Errorf("invalid task index: %d", taskIndex)
	}

	task := m.tasks[taskIndex]
	
	m.execMutex.Lock()
	defer m.execMutex.Unlock()

	// 建立執行記錄
	execution := TaskExecution{
		Task:      &task,
		StartTime: time.Now(),
		Status:    "running",
	}

	m.currentExec = &execution
	m.executions = append(m.executions, execution)

	// 在背景執行任務
	go m.executeTaskAsync(&execution)

	return nil
}

// GetCurrentExecution 取得當前執行
func (m *Manager) GetCurrentExecution() *TaskExecution {
	m.execMutex.Lock()
	defer m.execMutex.Unlock()
	return m.currentExec
}

// GetExecutions 取得執行歷史
func (m *Manager) GetExecutions() []TaskExecution {
	m.execMutex.Lock()
	defer m.execMutex.Unlock()
	return append([]TaskExecution{}, m.executions...)
}

// GetSearchFilter 取得搜尋過濾器
func (m *Manager) GetSearchFilter() binding.String {
	return m.searchFilter
}

// GetOutputBuffer 取得輸出緩衝區
func (m *Manager) GetOutputBuffer() binding.String {
	return m.outputBuffer
}

// SetSelectedTask 設定選中的任務
func (m *Manager) SetSelectedTask(index int) {
	m.selectedTask = index
}

// GetSelectedTask 取得選中的任務
func (m *Manager) GetSelectedTask() int {
	return m.selectedTask
}
